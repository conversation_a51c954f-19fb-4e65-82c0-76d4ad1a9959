import request from "@/utils/request";

// 查询人员基本信息列表
export function listInfo(query) {
  return request({
    url: "/person/info/list",
    method: "get",
    params: query,
  });
}

// 查询人员基本信息详细
export function getInfo(userId) {
  return request({
    url: "/person/info/" + userId,
    method: "get",
  });
}

// 新增人员基本信息
export function addInfo(data) {
  return request({
    url: "/person/info",
    method: "post",
    data: data,
  });
}

// 修改人员基本信息
export function updateInfo(data) {
  return request({
    url: "/person/info",
    method: "put",
    data: data,
  });
}

// 删除人员基本信息
export function delInfo(userId) {
  return request({
    url: "/person/info/" + userId,
    method: "delete",
  });
}

// 修改人员基本信息
export function updateInfoType(data, type) {
  return request({
    url: "/person/info/" + type,
    method: "put",
    data: data,
  });
}

// 修改人员基本信息
export function updateInfoClear(type, userId) {
  return request({
    url: "/person/info/" + type + "/" + userId,
    method: "put",
  });
}
