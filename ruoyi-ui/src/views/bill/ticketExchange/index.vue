<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="">
        <el-date-picker
          v-model="daterangeCreateTime"
          type="daterange"
          range-separator="-"
          value-format="yyyy-MM-dd"
          start-placeholder="开始申请日期"
          end-placeholder="结束申请日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="ticketAcc">
        <el-input
          v-model="queryParams.ticketAcc"
          placeholder="请输入交易所账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择审核状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_ticket_exchange_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="是否导出" prop="exportType">
        <el-select
          v-model="queryParams.exportType"
          placeholder="请选择是否导出"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ticket:exchange:export']"
          >导出</el-button
        >
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['consume:systemGive:add']"
          >导入审核</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <div
      style="
        display: flex;
        color: #ffffff;
        text-align: center;
        justify-content: space-around;
        padding: 20px;
      "
    >
      <div style="background-color: #ffa800; min-width: 15%">
        <div style="font-size: 34px">{{ exchangeQzSum || 0 }}</div>
        <div style="font-size: 20px">权证兑换总额</div>
      </div>
      <div style="background-color: #ffa800; min-width: 15%">
        <div style="font-size: 34px">{{ exchangeQzSumPiao || 0 }}</div>
        <div style="font-size: 20px">权证兑票总额</div>
      </div>
      <div style="background-color: #409eff; min-width: 15%">
        <div style="font-size: 34px">{{ exchangeYjSum || 0 }}</div>
        <div style="font-size: 20px">佣金兑换总额</div>
      </div>
      <div style="background-color: #409eff; min-width: 15%">
        <div style="font-size: 34px">{{ exchangeYjSumPiao || 0 }}</div>
        <div style="font-size: 20px">佣金兑票总额</div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="exchangeList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="交易所账号" align="center" prop="ticketAcc" />
      <el-table-column label="兑换类型" align="center" prop="type">
        <template slot-scope="scope">
          {{ scope.row.type == 1 ? "权证" : scope.row.type == 2 ? "佣金" : "" }}
        </template>
      </el-table-column>
      <el-table-column label="承兑商账号" align="center" prop="acceptorAcc" />
      <el-table-column label="兑换额" align="center" prop="exchangeAmount" />
      <el-table-column label="票编号" align="center" prop="ticketNumber" />
      <el-table-column label="票名称" align="center" prop="ticketName" />
      <el-table-column label="手续费" align="center" prop="charge" />
      <el-table-column label="兑换比例" align="center" prop="proportion" />
      <el-table-column label="申请时间" align="center" prop="createTime" />
      <el-table-column label="审核状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_ticket_exchange_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="updateTime" />
      <!-- <el-table-column label="导出状态" align="center" prop="exportType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_yes_no"
            :value="scope.row.exportType"
          />
        </template>
      </el-table-column> -->
      <el-table-column label="兑换票证" align="center" prop="ticket" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="200"
      >
        <template slot-scope="scope">
          <el-button
            type="success"
            size="small"
            v-if="scope.row.status == 0"
            @click="handleUpdate(scope.row, 1)"
            v-hasPermi="['ticket:exchange:examine']"
            >通过</el-button
          >
          <el-button
            type="danger"
            size="small"
            v-if="scope.row.status == 0"
            @click="handleUpdate(scope.row, 2)"
            v-hasPermi="['ticket:exchange:examine']"
            >拒绝</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数字权证兑换票证对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户主键" />
        </el-form-item>
        <el-form-item label="手续费" prop="charge">
          <el-input v-model="form.charge" placeholder="请输入手续费" />
        </el-form-item>
        <el-form-item label="兑换比例" prop="proportion">
          <el-input v-model="form.proportion" placeholder="请输入兑换比例" />
        </el-form-item>
        <el-form-item label="兑换额" prop="exchangeAmount">
          <el-input v-model="form.exchangeAmount" placeholder="请输入兑换额" />
        </el-form-item>
        <el-form-item label="兑换票证" prop="ticket">
          <el-input v-model="form.ticket" placeholder="请输入兑换票证" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listExchange,
  getExchange,
  delExchange,
  addExchange,
  updateExchange,
  listExchangeSum,
} from "@/api/bill/ticket/exchange";
import { getToken } from "@/utils/auth";
export default {
  name: "Exchange",
  dicts: ["sxsc_ticket_exchange_status", "sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数字权证兑换票证表格数据
      exchangeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //申请时间查询范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        charge: null,
        proportion: null,
        exchangeAmount: null,
        status: null,
        exportType: null,
        ticketAcc: null,
        params: {
          phonenumber: null,
          endCreateTime: null,
          beginCreateTime: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户主键不能为空", trigger: "blur" },
        ],
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/bill/ticket/exchange/importData",
      },
      //权证兑换总数
      exchangeQzSum: 0,
      exchangeQzSumPiao: 0,
      //佣金兑换总数
      exchangeYjSum: 0,
      exchangeYjSumPiao: 0,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询数字权证兑换票证列表 */
    getList() {
      this.loading = true;
      if (null != this.daterangeCreateTime && "" != this.daterangeCreateTime) {
        this.queryParams.params.beginCreateTime = this.daterangeCreateTime[0];
        this.queryParams.params.endCreateTime = this.daterangeCreateTime[1];
      } else {
        this.queryParams.params.beginCreateTime = null;
        this.queryParams.params.endCreateTime = null;
      }
      listExchange(this.queryParams).then((response) => {
        this.exchangeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.exchangeQzSum = 0;
      this.exchangeQzSumPiao = 0;
      this.exchangeQzSum = 0;
      this.exchangeYjSumPiao = 0;
      listExchangeSum(this.queryParams).then((response) => {
        let data = response.data;
        data.forEach((element) => {
          if (element.type == 1) {
            this.exchangeQzSum = element.totalExchangeAmount;
            this.exchangeQzSumPiao = element.totalTicket;
          }
          if (element.type == 2) {
            this.exchangeYjSum = element.totalExchangeAmount;
            this.exchangeYjSumPiao = element.totalTicket;
          }
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        charge: null,
        proportion: null,
        exchangeAmount: null,
        status: null,
        exportType: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        ticket: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数字权证兑换票证";
    },
    /** 修改按钮操作 */
    handleUpdate(row, stauts) {
      const id = row.id || this.ids;
      let data = {
        id: id,
        status: stauts,
      };
      updateExchange(data).then((response) => {
        this.$modal.msgSuccess("审核成功");
        this.open = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateExchange(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExchange(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除数字权证兑换票证编号为"' + ids + '"的数据项？')
        .then(function () {
          return delExchange(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "bill/ticket/exchange/export",
        {
          ...this.queryParams,
        },
        `数字权证兑换票证_${this.parseTime(
          new Date().getTime(),
          "{y}-{m}-{d}"
        )}.xlsx`
      );
    },
  },
};
</script>
